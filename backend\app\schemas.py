from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: "UserOut"

class UserCreate(BaseModel):
    name: str
    email: EmailStr
    password: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class OAuthUserCreate(BaseModel):
    name: str
    email: EmailStr
    provider: str
    provider_id: str
    avatar_url: Optional[str] = None

class UserOut(BaseModel):
    id: int
    name: str
    email: EmailStr
    created_at: datetime
    avatar_url: Optional[str] = None
    is_oauth_user: bool = False
    class Config:
        from_attributes = True

class ResearchProfile(BaseModel):
    name: str
    research_area: str
    experience_level: str
    interests: str
    past_work: Optional[str] = None
    resources: Optional[str] = None
    timeline: Optional[str] = None

class ResearchIdeaBase(BaseModel):
    title: str
    description: str
    difficulty: Optional[str] = "Medium"
    feasibility_score: Optional[int] = 3
    estimated_duration: Optional[str] = None
    required_resources: Optional[str] = None
    methodology: Optional[str] = None
    expected_outcomes: Optional[str] = None
    detailed_plan: Optional[str] = None
    research_area: Optional[str] = None
    notes: Optional[str] = None
    status: Optional[str] = "active"

class ResearchIdeaCreate(ResearchIdeaBase):
    pass

class ResearchIdeaUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    difficulty: Optional[str] = None
    feasibility_score: Optional[int] = None
    estimated_duration: Optional[str] = None
    required_resources: Optional[str] = None
    methodology: Optional[str] = None
    expected_outcomes: Optional[str] = None
    detailed_plan: Optional[str] = None
    research_area: Optional[str] = None
    notes: Optional[str] = None
    status: Optional[str] = None

class ResearchIdeaOut(ResearchIdeaBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    class Config:
        from_attributes = True

class GenerateIdeasRequest(BaseModel):
    name: str
    research_area: str
    experience_level: str
    interests: str
    past_work: Optional[str] = None
    resources: Optional[str] = None
    timeline: Optional[str] = None

class RefineIdeaRequest(BaseModel):
    feedback: str

class CompareIdeasRequest(BaseModel):
    idea1_id: int
    idea2_id: int

class GenerateIdeasResponse(BaseModel):
    ideas: List[ResearchIdeaBase]

class ComparisonResponse(BaseModel):
    idea1: ResearchIdeaOut
    idea2: ResearchIdeaOut
    comparison: str
    pros_cons: dict

class TokenOut(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: UserOut

