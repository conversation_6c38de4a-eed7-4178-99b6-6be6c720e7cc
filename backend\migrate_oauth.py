#!/usr/bin/env python3
"""
OAuth Migration Script
Adds OAuth fields to existing User table
"""

import sqlite3
import os
from pathlib import Path

def migrate_database():
    """Add OAuth fields to the users table"""
    
    # Get database path from environment or use default
    db_path = os.getenv('DATABASE_URL', 'sqlite+aiosqlite:///./agentresearchhub.db')
    
    # Extract the actual file path from SQLAlchemy URL
    if db_path.startswith('sqlite'):
        db_file = db_path.split(':///')[-1]
    else:
        print("This migration script only supports SQLite databases")
        return False
    
    if not Path(db_file).exists():
        print(f"Database file {db_file} does not exist. Please run init_db.py first.")
        return False
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # Check if OAuth columns already exist
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        oauth_columns = [
            'google_id', 'facebook_id', 'twitter_id', 
            'avatar_url', 'is_oauth_user'
        ]
        
        missing_columns = [col for col in oauth_columns if col not in columns]
        
        if not missing_columns:
            print("OAuth columns already exist. No migration needed.")
            return True
        
        print(f"Adding missing OAuth columns: {missing_columns}")
        
        # Add missing columns
        migrations = []
        
        if 'google_id' in missing_columns:
            migrations.append("ALTER TABLE users ADD COLUMN google_id VARCHAR(255)")
            migrations.append("CREATE UNIQUE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id)")
        
        if 'facebook_id' in missing_columns:
            migrations.append("ALTER TABLE users ADD COLUMN facebook_id VARCHAR(255)")
            migrations.append("CREATE UNIQUE INDEX IF NOT EXISTS idx_users_facebook_id ON users(facebook_id)")
        
        if 'twitter_id' in missing_columns:
            migrations.append("ALTER TABLE users ADD COLUMN twitter_id VARCHAR(255)")
            migrations.append("CREATE UNIQUE INDEX IF NOT EXISTS idx_users_twitter_id ON users(twitter_id)")
        
        if 'avatar_url' in missing_columns:
            migrations.append("ALTER TABLE users ADD COLUMN avatar_url VARCHAR(500)")
        
        if 'is_oauth_user' in missing_columns:
            migrations.append("ALTER TABLE users ADD COLUMN is_oauth_user BOOLEAN DEFAULT 0")
        
        # Make password_hash nullable for OAuth users
        if 'password_hash' in columns:
            # SQLite doesn't support ALTER COLUMN, so we need to check if it's already nullable
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='users'")
            table_sql = cursor.fetchone()[0]
            
            if 'password_hash VARCHAR(255) NOT NULL' in table_sql:
                print("Note: password_hash column is NOT NULL. Consider recreating the table to make it nullable for OAuth users.")
                print("For now, OAuth users will have empty password hashes.")
        
        # Execute migrations
        for migration in migrations:
            print(f"Executing: {migration}")
            cursor.execute(migration)
        
        # Update existing users to have is_oauth_user = False
        cursor.execute("UPDATE users SET is_oauth_user = 0 WHERE is_oauth_user IS NULL")
        
        conn.commit()
        print("OAuth migration completed successfully!")
        
        # Show updated table structure
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        print("\nUpdated table structure:")
        for col in columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        
        return True
        
    except Exception as e:
        print(f"Migration failed: {e}")
        return False
    
    finally:
        if conn:
            conn.close()

def verify_migration():
    """Verify that the migration was successful"""
    
    db_path = os.getenv('DATABASE_URL', 'sqlite+aiosqlite:///./agentresearchhub.db')
    
    if db_path.startswith('sqlite'):
        db_file = db_path.split(':///')[-1]
    else:
        return False
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # Check if all OAuth columns exist
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        required_columns = [
            'google_id', 'facebook_id', 'twitter_id', 
            'avatar_url', 'is_oauth_user'
        ]
        
        missing = [col for col in required_columns if col not in columns]
        
        if missing:
            print(f"Verification failed. Missing columns: {missing}")
            return False
        
        print("Migration verification successful!")
        return True
        
    except Exception as e:
        print(f"Verification failed: {e}")
        return False
    
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("Starting OAuth database migration...")
    
    if migrate_database():
        if verify_migration():
            print("\n✅ OAuth migration completed and verified successfully!")
            print("\nYou can now use social login features.")
            print("Make sure to configure your OAuth providers in the .env file.")
        else:
            print("\n❌ Migration completed but verification failed.")
    else:
        print("\n❌ Migration failed.")
        print("Please check the error messages above and try again.")
