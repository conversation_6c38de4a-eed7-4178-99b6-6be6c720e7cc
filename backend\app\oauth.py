import httpx
from typing import Dict, Optional, Any
from urllib.parse import urlenco<PERSON>
from .settings import settings


class OAuthProvider:
    """Base OAuth provider class"""
    
    def __init__(self, client_id: str, client_secret: str):
        self.client_id = client_id
        self.client_secret = client_secret
    
    def get_authorization_url(self, redirect_uri: str, state: str) -> str:
        """Get the authorization URL for OAuth flow"""
        raise NotImplementedError
    
    async def get_access_token(self, code: str, redirect_uri: str) -> Optional[str]:
        """Exchange authorization code for access token"""
        raise NotImplementedError
    
    async def get_user_info(self, access_token: str) -> Optional[Dict[str, Any]]:
        """Get user information using access token"""
        raise NotImplementedError


class GoogleOAuth(OAuthProvider):
    """Google OAuth provider"""

    def __init__(self):
        super().__init__(settings.GOOGLE_CLIENT_ID or "dummy", settings.GOOGLE_CLIENT_SECRET or "dummy")
        self.auth_url = "https://accounts.google.com/o/oauth2/v2/auth"
        self.token_url = "https://oauth2.googleapis.com/token"
        self.user_info_url = "https://www.googleapis.com/oauth2/v2/userinfo"
    
    def get_authorization_url(self, redirect_uri: str, state: str) -> str:
        params = {
            "client_id": self.client_id,
            "redirect_uri": redirect_uri,
            "scope": "openid email profile",
            "response_type": "code",
            "state": state,
            "access_type": "offline",
            "prompt": "consent"
        }
        return f"{self.auth_url}?{urlencode(params)}"
    
    async def get_access_token(self, code: str, redirect_uri: str) -> Optional[str]:
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri,
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(self.token_url, data=data)
                response.raise_for_status()
                token_data = response.json()
                return token_data.get("access_token")
            except Exception as e:
                print(f"Error getting Google access token: {e}")
                return None
    
    async def get_user_info(self, access_token: str) -> Optional[Dict[str, Any]]:
        headers = {"Authorization": f"Bearer {access_token}"}
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(self.user_info_url, headers=headers)
                response.raise_for_status()
                user_data = response.json()
                
                return {
                    "id": user_data.get("id"),
                    "email": user_data.get("email"),
                    "name": user_data.get("name"),
                    "avatar_url": user_data.get("picture"),
                    "provider": "google"
                }
            except Exception as e:
                print(f"Error getting Google user info: {e}")
                return None


class FacebookOAuth(OAuthProvider):
    """Facebook OAuth provider"""

    def __init__(self):
        super().__init__(settings.FACEBOOK_CLIENT_ID or "dummy", settings.FACEBOOK_CLIENT_SECRET or "dummy")
        self.auth_url = "https://www.facebook.com/v18.0/dialog/oauth"
        self.token_url = "https://graph.facebook.com/v18.0/oauth/access_token"
        self.user_info_url = "https://graph.facebook.com/v18.0/me"
    
    def get_authorization_url(self, redirect_uri: str, state: str) -> str:
        params = {
            "client_id": self.client_id,
            "redirect_uri": redirect_uri,
            "scope": "email,public_profile",
            "response_type": "code",
            "state": state,
        }
        return f"{self.auth_url}?{urlencode(params)}"
    
    async def get_access_token(self, code: str, redirect_uri: str) -> Optional[str]:
        params = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "redirect_uri": redirect_uri,
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(self.token_url, params=params)
                response.raise_for_status()
                token_data = response.json()
                return token_data.get("access_token")
            except Exception as e:
                print(f"Error getting Facebook access token: {e}")
                return None
    
    async def get_user_info(self, access_token: str) -> Optional[Dict[str, Any]]:
        params = {
            "access_token": access_token,
            "fields": "id,name,email,picture.type(large)"
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(self.user_info_url, params=params)
                response.raise_for_status()
                user_data = response.json()
                
                avatar_url = None
                if "picture" in user_data and "data" in user_data["picture"]:
                    avatar_url = user_data["picture"]["data"].get("url")
                
                return {
                    "id": user_data.get("id"),
                    "email": user_data.get("email"),
                    "name": user_data.get("name"),
                    "avatar_url": avatar_url,
                    "provider": "facebook"
                }
            except Exception as e:
                print(f"Error getting Facebook user info: {e}")
                return None


class TwitterOAuth(OAuthProvider):
    """Twitter/X OAuth provider using OAuth 2.0 PKCE"""

    def __init__(self):
        super().__init__(settings.TWITTER_CLIENT_ID or "dummy", settings.TWITTER_CLIENT_SECRET or "dummy")
        self.auth_url = "https://twitter.com/i/oauth2/authorize"
        self.token_url = "https://api.twitter.com/2/oauth2/token"
        self.user_info_url = "https://api.twitter.com/2/users/me"
    
    def get_authorization_url(self, redirect_uri: str, state: str) -> str:
        params = {
            "response_type": "code",
            "client_id": self.client_id,
            "redirect_uri": redirect_uri,
            "scope": "tweet.read users.read offline.access",
            "state": state,
            "code_challenge": "challenge",  # In production, use proper PKCE
            "code_challenge_method": "plain"
        }
        return f"{self.auth_url}?{urlencode(params)}"
    
    async def get_access_token(self, code: str, redirect_uri: str) -> Optional[str]:
        data = {
            "code": code,
            "grant_type": "authorization_code",
            "client_id": self.client_id,
            "redirect_uri": redirect_uri,
            "code_verifier": "challenge"  # In production, use proper PKCE
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    self.token_url, 
                    data=data, 
                    headers=headers,
                    auth=(self.client_id, self.client_secret)
                )
                response.raise_for_status()
                token_data = response.json()
                return token_data.get("access_token")
            except Exception as e:
                print(f"Error getting Twitter access token: {e}")
                return None
    
    async def get_user_info(self, access_token: str) -> Optional[Dict[str, Any]]:
        headers = {"Authorization": f"Bearer {access_token}"}
        params = {"user.fields": "id,name,username,profile_image_url"}
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(self.user_info_url, headers=headers, params=params)
                response.raise_for_status()
                response_data = response.json()
                user_data = response_data.get("data", {})
                
                return {
                    "id": user_data.get("id"),
                    "email": f"{user_data.get('username')}@twitter.placeholder",  # Twitter doesn't provide email
                    "name": user_data.get("name"),
                    "avatar_url": user_data.get("profile_image_url"),
                    "provider": "twitter"
                }
            except Exception as e:
                print(f"Error getting Twitter user info: {e}")
                return None


# OAuth provider instances
oauth_providers = {
    "google": GoogleOAuth(),
    "facebook": FacebookOAuth(),
    "twitter": TwitterOAuth(),
}
