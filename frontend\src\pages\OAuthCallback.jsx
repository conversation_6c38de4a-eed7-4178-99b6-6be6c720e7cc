import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Loader2, CheckCircle, XCircle } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext.jsx'
import { api } from '../services/api'
import toast from 'react-hot-toast'

const OAuthCallback = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { setUser, setToken } = useAuth() || {}
  const [status, setStatus] = useState('processing') // processing, success, error

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const token = params.get('token')
    const error = params.get('error')

    if (error) {
      setStatus('error')
      toast.error('Authentication failed')
      setTimeout(() => navigate('/login', { replace: true }), 2000)
      return
    }

    if (!token) {
      setStatus('error')
      toast.error('Authentication failed: No token received')
      setTimeout(() => navigate('/login', { replace: true }), 2000)
      return
    }

    // Store token and fetch user
    localStorage.setItem('token', token)
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`
    if (setToken) setToken(token)

    api.get('/auth/me')
      .then((res) => {
        if (setUser) setUser(res.data)
        setStatus('success')
        toast.success('Signed in successfully!')
        setTimeout(() => navigate('/dashboard', { replace: true }), 1500)
      })
      .catch(() => {
        setStatus('error')
        toast.error('Could not fetch user info')
        setTimeout(() => navigate('/login', { replace: true }), 2000)
      })
  }, [location.search, navigate, setUser, setToken])

  const getStatusContent = () => {
    switch (status) {
      case 'processing':
        return {
          icon: <Loader2 className="w-8 h-8 animate-spin text-primary-600" />,
          title: 'Completing sign-in...',
          message: 'Please wait while we authenticate your account.'
        }
      case 'success':
        return {
          icon: <CheckCircle className="w-8 h-8 text-green-600" />,
          title: 'Success!',
          message: 'You have been signed in successfully. Redirecting...'
        }
      case 'error':
        return {
          icon: <XCircle className="w-8 h-8 text-red-600" />,
          title: 'Authentication Failed',
          message: 'There was an error signing you in. Redirecting to login...'
        }
      default:
        return {
          icon: <Loader2 className="w-8 h-8 animate-spin text-primary-600" />,
          title: 'Processing...',
          message: 'Please wait...'
        }
    }
  }

  const content = getStatusContent()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-secondary-900 dark:to-primary-900">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center p-8 bg-white dark:bg-secondary-800 rounded-xl shadow-lg max-w-md w-full mx-4"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          className="flex justify-center mb-4"
        >
          {content.icon}
        </motion.div>

        <motion.h2
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="text-xl font-semibold text-secondary-900 dark:text-secondary-100 mb-2"
        >
          {content.title}
        </motion.h2>

        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="text-secondary-600 dark:text-secondary-300"
        >
          {content.message}
        </motion.p>
      </motion.div>
    </div>
  )
}

export default OAuthCallback

