from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from io import BytesIO
import markdown
from datetime import datetime
from .. import models

class ExportService:
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Setup custom styles for PDF generation"""
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            textColor=colors.HexColor('#2563eb')
        ))
        
        self.styles.add(ParagraphStyle(
            name='CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceBefore=20,
            spaceAfter=10,
            textColor=colors.HexColor('#1e40af')
        ))
        
        self.styles.add(ParagraphStyle(
            name='CustomBody',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            leading=14
        ))
    
    def export_to_pdf(self, idea: models.ResearchIdea) -> bytes:
        """Export research idea to PDF format"""
        buffer = BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin = 18
        )
        
        # Build the PDF content
        story = []
        
        # Title
        story.append(Paragraph(idea.title, self.styles['CustomTitle']))
        story.append(Spacer(1, 12))
        
        # Metadata table
        metadata = [
            ['Research Area', idea.research_area or 'Not specified'],
            ['Difficulty', idea.difficulty],
            ['Feasibility Score', f"{idea.feasibility_score}/5 stars"],
            ['Estimated Duration', idea.estimated_duration or 'Not specified'],
            ['Status', idea.status.title()],
            ['Created', idea.created_at.strftime('%B %d, %Y')],
            ['Last Updated', idea.updated_at.strftime('%B %d, %Y')]
        ]
        
        metadata_table = Table(metadata, colWidths=[2*inch, 4*inch])
        metadata_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f3f4f6')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.HexColor('#374151')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#e5e7eb')),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        story.append(metadata_table)
        story.append(Spacer(1, 20))
        
        # Description
        story.append(Paragraph('Description', self.styles['CustomHeading']))
        story.append(Paragraph(idea.description, self.styles['CustomBody']))
        story.append(Spacer(1, 12))
        
        # Methodology
        if idea.methodology:
            story.append(Paragraph('Methodology', self.styles['CustomHeading']))
            story.append(Paragraph(idea.methodology, self.styles['CustomBody']))
            story.append(Spacer(1, 12))
        
        # Expected Outcomes
        if idea.expected_outcomes:
            story.append(Paragraph('Expected Outcomes', self.styles['CustomHeading']))
            story.append(Paragraph(idea.expected_outcomes, self.styles['CustomBody']))
            story.append(Spacer(1, 12))
        
        # Required Resources
        if idea.required_resources:
            story.append(Paragraph('Required Resources', self.styles['CustomHeading']))
            story.append(Paragraph(idea.required_resources, self.styles['CustomBody']))
            story.append(Spacer(1, 12))
        
        # Detailed Plan
        if idea.detailed_plan:
            story.append(Paragraph('Implementation Plan', self.styles['CustomHeading']))
            # Split detailed plan by lines and create numbered list
            plan_lines = idea.detailed_plan.split('\n')
            for i, line in enumerate(plan_lines, 1):
                if line.strip():
                    story.append(Paragraph(f"{i}. {line.strip()}", self.styles['CustomBody']))
            story.append(Spacer(1, 12))
        
        # Notes
        if idea.notes:
            story.append(Paragraph('Additional Notes', self.styles['CustomHeading']))
            story.append(Paragraph(idea.notes, self.styles['CustomBody']))
        
        # Footer
        story.append(Spacer(1, 30))
        footer_text = f"Generated by AgentResearchHub on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}"
        story.append(Paragraph(footer_text, self.styles['Normal']))
        
        # Build PDF
        doc.build(story)
        
        # Get the PDF content
        buffer.seek(0)
        return buffer.read()
    
    def export_to_markdown(self, idea: models.ResearchIdea) -> str:
        """Export research idea to Markdown format"""
        md_content = []
        
        # Title
        md_content.append(f"# {idea.title}\n")
        
        # Metadata
        md_content.append("## Research Information\n")
        md_content.append(f"- **Research Area:** {idea.research_area or 'Not specified'}")
        md_content.append(f"- **Difficulty:** {idea.difficulty}")
        md_content.append(f"- **Feasibility Score:** {idea.feasibility_score}/5 ⭐")
        md_content.append(f"- **Estimated Duration:** {idea.estimated_duration or 'Not specified'}")
        md_content.append(f"- **Status:** {idea.status.title()}")
        md_content.append(f"- **Created:** {idea.created_at.strftime('%B %d, %Y')}")
        md_content.append(f"- **Last Updated:** {idea.updated_at.strftime('%B %d, %Y')}\n")
        
        # Description
        md_content.append("## Description\n")
        md_content.append(f"{idea.description}\n")
        
        # Methodology
        if idea.methodology:
            md_content.append("## Methodology\n")
            md_content.append(f"{idea.methodology}\n")
        
        # Expected Outcomes
        if idea.expected_outcomes:
            md_content.append("## Expected Outcomes\n")
            md_content.append(f"{idea.expected_outcomes}\n")
        
        # Required Resources
        if idea.required_resources:
            md_content.append("## Required Resources\n")
            md_content.append(f"{idea.required_resources}\n")
        
        # Implementation Plan
        if idea.detailed_plan:
            md_content.append("## Implementation Plan\n")
            plan_lines = idea.detailed_plan.split('\n')
            for i, line in enumerate(plan_lines, 1):
                if line.strip():
                    md_content.append(f"{i}. {line.strip()}")
            md_content.append("")
        
        # Notes
        if idea.notes:
            md_content.append("## Additional Notes\n")
            md_content.append(f"{idea.notes}\n")
        
        # Footer
        md_content.append("---")
        md_content.append(f"*Generated by AgentResearchHub on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}*")
        
        return '\n'.join(md_content)
    
    def export_comparison_to_pdf(self, idea1: models.ResearchIdea, idea2: models.ResearchIdea, comparison_data: dict) -> bytes:
        """Export idea comparison to PDF format"""
        buffer = BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        story = []
        
        # Title
        story.append(Paragraph("Research Ideas Comparison", self.styles['CustomTitle']))
        story.append(Spacer(1, 20))
        
        # Ideas being compared
        story.append(Paragraph("Ideas Being Compared", self.styles['CustomHeading']))
        
        comparison_table = [
            ['Aspect', 'Idea 1', 'Idea 2'],
            ['Title', idea1.title, idea2.title],
            ['Difficulty', idea1.difficulty, idea2.difficulty],
            ['Feasibility Score', f"{idea1.feasibility_score}/5", f"{idea2.feasibility_score}/5"],
            ['Duration', idea1.estimated_duration or 'Not specified', idea2.estimated_duration or 'Not specified']
        ]
        
        table = Table(comparison_table, colWidths=[1.5*inch, 2.25*inch, 2.25*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2563eb')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#e5e7eb')),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        # Comparison analysis
        story.append(Paragraph("Detailed Comparison", self.styles['CustomHeading']))
        story.append(Paragraph(comparison_data.get('comparison', 'No comparison available'), self.styles['CustomBody']))
        story.append(Spacer(1, 20))
        
        # Pros and Cons
        if 'pros_cons' in comparison_data:
            pros_cons = comparison_data['pros_cons']
            
            story.append(Paragraph("Pros and Cons Analysis", self.styles['CustomHeading']))
            
            # Idea 1 Pros/Cons
            story.append(Paragraph(f"**{idea1.title}**", self.styles['CustomBody']))
            
            if 'idea1_pros' in pros_cons:
                story.append(Paragraph("Pros:", self.styles['CustomBody']))
                for pro in pros_cons['idea1_pros']:
                    story.append(Paragraph(f"• {pro}", self.styles['CustomBody']))
            
            if 'idea1_cons' in pros_cons:
                story.append(Paragraph("Cons:", self.styles['CustomBody']))
                for con in pros_cons['idea1_cons']:
                    story.append(Paragraph(f"• {con}", self.styles['CustomBody']))
            
            story.append(Spacer(1, 12))
            
            # Idea 2 Pros/Cons
            story.append(Paragraph(f"**{idea2.title}**", self.styles['CustomBody']))
            
            if 'idea2_pros' in pros_cons:
                story.append(Paragraph("Pros:", self.styles['CustomBody']))
                for pro in pros_cons['idea2_pros']:
                    story.append(Paragraph(f"• {pro}", self.styles['CustomBody']))
            
            if 'idea2_cons' in pros_cons:
                story.append(Paragraph("Cons:", self.styles['CustomBody']))
                for con in pros_cons['idea2_cons']:
                    story.append(Paragraph(f"• {con}", self.styles['CustomBody']))
        
        # Footer
        story.append(Spacer(1, 30))
        footer_text = f"Comparison generated by AgentResearchHub on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}"
        story.append(Paragraph(footer_text, self.styles['Normal']))
        
        # Build PDF
        doc.build(story)
        
        # Get the PDF content
        buffer.seek(0)
        return buffer.read()
