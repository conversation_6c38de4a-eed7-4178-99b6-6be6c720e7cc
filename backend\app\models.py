from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, func, Float, Boolean
from sqlalchemy.orm import relationship
from .db import Base

class User(Base):
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    name = Column(String(255), nullable=False)
    password_hash = Column(String(255), nullable=True)  # Made nullable for OAuth users
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # OAuth fields
    google_id = Column(String(255), unique=True, nullable=True, index=True)
    facebook_id = Column(String(255), unique=True, nullable=True, index=True)
    twitter_id = Column(String(255), unique=True, nullable=True, index=True)
    avatar_url = Column(String(500), nullable=True)
    is_oauth_user = Column(Boolean, default=False)

    ideas = relationship("ResearchIdea", back_populates="user")

class ResearchIdea(Base):
    __tablename__ = 'research_ideas'
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    title = Column(String(500), nullable=False)
    description = Column(Text, nullable=False)
    difficulty = Column(String(50), default='Medium')  # Easy, Medium, Hard
    feasibility_score = Column(Integer, default=3)  # 1-5 stars
    estimated_duration = Column(String(100))
    required_resources = Column(Text)
    methodology = Column(Text)
    expected_outcomes = Column(Text)
    detailed_plan = Column(Text)
    research_area = Column(String(100))
    notes = Column(Text)
    status = Column(String(50), default='active')  # active, archived, completed
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    user = relationship("User", back_populates="ideas")

