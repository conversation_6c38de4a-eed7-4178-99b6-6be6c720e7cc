#!/usr/bin/env python3
"""
OAuth Testing Script
Tests the OAuth implementation without requiring actual OAuth providers
"""

import asyncio
import sys
import os
from unittest.mock import AsyncMock, patch

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.oauth import oauth_providers, GoogleOAuth, FacebookOAuth, TwitterOAuth
from app.settings import settings

async def test_oauth_provider_initialization():
    """Test that OAuth providers are properly initialized"""
    print("Testing OAuth provider initialization...")
    
    # Test provider availability
    expected_providers = ['google', 'facebook', 'twitter']
    for provider in expected_providers:
        if provider in oauth_providers:
            print(f"✅ {provider.capitalize()} provider initialized")
        else:
            print(f"❌ {provider.capitalize()} provider missing")
    
    # Test provider types
    assert isinstance(oauth_providers['google'], GoogleOAuth)
    assert isinstance(oauth_providers['facebook'], FacebookOAuth)
    assert isinstance(oauth_providers['twitter'], TwitterOAuth)
    
    print("✅ All OAuth providers initialized correctly")

async def test_authorization_urls():
    """Test that authorization URLs are generated correctly"""
    print("\nTesting authorization URL generation...")
    
    test_redirect_uri = "http://localhost:8000/auth/test/callback"
    test_state = "test_state_123"
    
    for provider_name, provider in oauth_providers.items():
        try:
            auth_url = provider.get_authorization_url(test_redirect_uri, test_state)
            
            # Basic URL validation
            assert auth_url.startswith('https://'), f"{provider_name} URL should use HTTPS"
            assert test_redirect_uri in auth_url, f"{provider_name} URL should contain redirect URI"
            assert test_state in auth_url, f"{provider_name} URL should contain state parameter"
            
            print(f"✅ {provider_name.capitalize()} authorization URL: {auth_url[:80]}...")
            
        except Exception as e:
            print(f"❌ {provider_name.capitalize()} authorization URL generation failed: {e}")

async def test_mock_oauth_flow():
    """Test OAuth flow with mocked responses"""
    print("\nTesting OAuth flow with mocked responses...")
    
    # Mock successful responses
    mock_token_response = {"access_token": "mock_access_token"}
    mock_user_info = {
        "id": "123456789",
        "email": "<EMAIL>",
        "name": "Test User",
        "avatar_url": "https://example.com/avatar.jpg",
        "provider": "google"
    }
    
    for provider_name, provider in oauth_providers.items():
        print(f"\nTesting {provider_name.capitalize()} OAuth flow...")
        
        try:
            # Mock the HTTP requests
            with patch('httpx.AsyncClient') as mock_client:
                # Mock token exchange
                mock_response = AsyncMock()
                mock_response.json.return_value = mock_token_response
                mock_response.raise_for_status.return_value = None
                
                mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
                mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
                
                # Test token exchange
                token = await provider.get_access_token("mock_code", "http://localhost:8000/auth/test/callback")
                assert token == "mock_access_token", f"{provider_name} token exchange failed"
                print(f"  ✅ Token exchange successful")
                
                # Mock user info response
                mock_user_response = AsyncMock()
                mock_user_response.json.return_value = {
                    "id": "123456789",
                    "email": "<EMAIL>",
                    "name": "Test User",
                    "picture": "https://example.com/avatar.jpg"  # Google format
                }
                mock_user_response.raise_for_status.return_value = None
                
                mock_client.return_value.__aenter__.return_value.get.return_value = mock_user_response
                
                # Test user info retrieval
                user_info = await provider.get_user_info("mock_access_token")
                assert user_info is not None, f"{provider_name} user info retrieval failed"
                assert "id" in user_info, f"{provider_name} user info missing ID"
                assert "email" in user_info, f"{provider_name} user info missing email"
                assert "name" in user_info, f"{provider_name} user info missing name"
                
                print(f"  ✅ User info retrieval successful")
                print(f"  ✅ {provider_name.capitalize()} OAuth flow test passed")
                
        except Exception as e:
            print(f"  ❌ {provider_name.capitalize()} OAuth flow test failed: {e}")

def test_environment_configuration():
    """Test environment configuration"""
    print("\nTesting environment configuration...")
    
    required_settings = [
        'JWT_SECRET',
        'DATABASE_URL',
        'FRONTEND_URL'
    ]
    
    oauth_settings = [
        'GOOGLE_CLIENT_ID',
        'GOOGLE_CLIENT_SECRET',
        'FACEBOOK_CLIENT_ID',
        'FACEBOOK_CLIENT_SECRET',
        'TWITTER_CLIENT_ID',
        'TWITTER_CLIENT_SECRET'
    ]
    
    # Check required settings
    for setting in required_settings:
        value = getattr(settings, setting, None)
        if value:
            print(f"✅ {setting}: configured")
        else:
            print(f"❌ {setting}: missing")
    
    # Check OAuth settings (optional but recommended)
    oauth_configured = 0
    for setting in oauth_settings:
        value = getattr(settings, setting, None)
        if value and value != "your-" in value.lower():
            print(f"✅ {setting}: configured")
            oauth_configured += 1
        else:
            print(f"⚠️  {setting}: not configured (use placeholder)")
    
    if oauth_configured == 0:
        print("\n⚠️  No OAuth providers configured. Social login will not work.")
        print("   Please update your .env file with OAuth credentials.")
    elif oauth_configured < len(oauth_settings):
        print(f"\n⚠️  Only {oauth_configured//2} out of 3 OAuth providers configured.")
    else:
        print("\n✅ All OAuth providers configured!")

async def main():
    """Run all tests"""
    print("🧪 Starting OAuth Implementation Tests")
    print("=" * 50)
    
    try:
        # Test environment configuration
        test_environment_configuration()
        
        # Test OAuth provider initialization
        await test_oauth_provider_initialization()
        
        # Test authorization URL generation
        await test_authorization_urls()
        
        # Test OAuth flow with mocks
        await test_mock_oauth_flow()
        
        print("\n" + "=" * 50)
        print("🎉 OAuth implementation tests completed!")
        print("\nNext steps:")
        print("1. Configure OAuth providers in your .env file")
        print("2. Start the backend server: uvicorn app.main:app --reload")
        print("3. Start the frontend server: npm run dev")
        print("4. Test social login at http://localhost:3000/login")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
