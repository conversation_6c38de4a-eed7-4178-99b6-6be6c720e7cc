from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    DATABASE_URL: str
    JWT_SECRET: str
    OPENAI_API_KEY: str | None = None
    CORS_ORIGINS: str = "http://localhost:3000"
    ENV: str = "development"

    # OAuth Settings
    GOOGLE_CLIENT_ID: str | None = None
    GOOGLE_CLIENT_SECRET: str | None = None
    FACEBOOK_CLIENT_ID: str | None = None
    FACEBOOK_CLIENT_SECRET: str | None = None
    TWITTER_CLIENT_ID: str | None = None
    TWITTER_CLIENT_SECRET: str | None = None

    # OAuth Redirect URLs
    FRONTEND_URL: str = "http://localhost:3000"
    OAUTH_REDIRECT_URL: str = "http://localhost:8000/auth/{provider}/callback"

    class Config:
        env_file = ".env"

settings = Settings()

