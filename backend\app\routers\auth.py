from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from urllib.parse import urlencode
import secrets
from .. import schemas, models
from ..db import get_db
from ..auth import hash_password, verify_password, create_access_token, get_current_user
from ..settings import settings
from ..oauth import oauth_providers

router = APIRouter()

@router.post('/register', response_model=schemas.TokenOut)
def register(user: schemas.UserCreate, db: Session = Depends(get_db)):
    # Check if user already exists
    existing = db.query(models.User).filter(models.User.email == user.email).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail='Email already registered'
        )

    # Create new user
    db_user = models.User(
        name=user.name,
        email=user.email,
        password_hash=hash_password(user.password),
        is_oauth_user=False
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # Create access token
    token = create_access_token({"sub": str(db_user.id)}, secret=settings.JWT_SECRET)

    return {
        "access_token": token,
        "token_type": "bearer",
        "user": db_user
    }

@router.post('/login', response_model=schemas.TokenOut)
def login(credentials: schemas.UserLogin, db: Session = Depends(get_db)):
    # Find user by email
    user = db.query(models.User).filter(models.User.email == credentials.email).first()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail='Invalid email or password'
        )

    # Check if user is OAuth-only user
    if user.is_oauth_user and not user.password_hash:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail='Please use social login for this account'
        )

    # Verify password for regular users
    if not verify_password(credentials.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail='Invalid email or password'
        )

    # Create access token
    token = create_access_token({"sub": str(user.id)}, secret=settings.JWT_SECRET)

    return {
        "access_token": token,
        "token_type": "bearer",
        "user": user
    }

@router.get('/me', response_model=schemas.UserOut)
def get_current_user_info(current_user: models.User = Depends(get_current_user)):
    return current_user


# OAuth Routes
@router.get('/{provider}/login')
async def oauth_login(provider: str, request: Request):
    """Initiate OAuth login flow"""
    if provider not in oauth_providers:
        raise HTTPException(status_code=400, detail="Unsupported OAuth provider")

    oauth_provider = oauth_providers[provider]

    # Generate state for CSRF protection
    state = secrets.token_urlsafe(32)

    # Store state in session (in production, use proper session management)
    redirect_uri = settings.OAUTH_REDIRECT_URL.format(provider=provider)

    # Get authorization URL
    auth_url = oauth_provider.get_authorization_url(redirect_uri, state)

    return RedirectResponse(url=auth_url)


@router.get('/{provider}/callback')
async def oauth_callback(provider: str, code: str, state: str = None, db: Session = Depends(get_db)):
    """Handle OAuth callback"""
    if provider not in oauth_providers:
        raise HTTPException(status_code=400, detail="Unsupported OAuth provider")

    if not code:
        raise HTTPException(status_code=400, detail="Authorization code not provided")

    oauth_provider = oauth_providers[provider]
    redirect_uri = settings.OAUTH_REDIRECT_URL.format(provider=provider)

    try:
        # Exchange code for access token
        access_token = await oauth_provider.get_access_token(code, redirect_uri)
        if not access_token:
            raise HTTPException(status_code=400, detail="Failed to get access token")

        # Get user info from provider
        user_info = await oauth_provider.get_user_info(access_token)
        if not user_info:
            raise HTTPException(status_code=400, detail="Failed to get user information")

        # Check if user exists
        user = None
        provider_id_field = f"{provider}_id"

        # Try to find user by provider ID first
        if provider == "google":
            user = db.query(models.User).filter(models.User.google_id == user_info["id"]).first()
        elif provider == "facebook":
            user = db.query(models.User).filter(models.User.facebook_id == user_info["id"]).first()
        elif provider == "twitter":
            user = db.query(models.User).filter(models.User.twitter_id == user_info["id"]).first()

        # If not found by provider ID, try by email
        if not user and user_info.get("email"):
            user = db.query(models.User).filter(models.User.email == user_info["email"]).first()

            # If user exists with email but no provider ID, link the accounts
            if user:
                if provider == "google":
                    user.google_id = user_info["id"]
                elif provider == "facebook":
                    user.facebook_id = user_info["id"]
                elif provider == "twitter":
                    user.twitter_id = user_info["id"]

                user.is_oauth_user = True
                if user_info.get("avatar_url"):
                    user.avatar_url = user_info["avatar_url"]

                db.commit()
                db.refresh(user)

        # Create new user if doesn't exist
        if not user:
            user_data = {
                "name": user_info["name"],
                "email": user_info["email"],
                "is_oauth_user": True,
                "avatar_url": user_info.get("avatar_url")
            }

            if provider == "google":
                user_data["google_id"] = user_info["id"]
            elif provider == "facebook":
                user_data["facebook_id"] = user_info["id"]
            elif provider == "twitter":
                user_data["twitter_id"] = user_info["id"]

            user = models.User(**user_data)
            db.add(user)
            db.commit()
            db.refresh(user)

        # Create JWT token
        token = create_access_token({"sub": str(user.id)}, secret=settings.JWT_SECRET)

        # Redirect to frontend with token
        frontend_url = f"{settings.FRONTEND_URL}/oauth/callback?token={token}"
        return RedirectResponse(url=frontend_url)

    except Exception as e:
        print(f"OAuth callback error: {e}")
        error_url = f"{settings.FRONTEND_URL}/login?error=oauth_failed"
        return RedirectResponse(url=error_url)

