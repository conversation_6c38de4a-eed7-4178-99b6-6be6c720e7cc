# OAuth Social Login Setup Guide

This guide will help you set up Google, Facebook, and X/Twitter social login for your ScholarAgent application.

## Prerequisites

1. Make sure you have installed the required dependencies:
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. Copy the environment example file:
   ```bash
   cp .env.example .env
   ```

## 1. Google OAuth Setup

### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API and Google OAuth2 API

### Step 2: Create OAuth 2.0 Credentials
1. Go to "Credentials" in the left sidebar
2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
3. Choose "Web application"
4. Add authorized redirect URIs:
   - `http://localhost:8000/auth/google/callback` (development)
   - `https://yourdomain.com/auth/google/callback` (production)
5. Copy the Client ID and Client Secret

### Step 3: Update Environment Variables
```env
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here
```

## 2. Facebook OAuth Setup

### Step 1: Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click "Create App" → "Consumer" → "Next"
3. Enter app name and contact email

### Step 2: Configure Facebook Login
1. In your app dashboard, click "Add Product" → "Facebook Login"
2. Choose "Web" platform
3. Add Valid OAuth Redirect URIs:
   - `http://localhost:8000/auth/facebook/callback` (development)
   - `https://yourdomain.com/auth/facebook/callback` (production)

### Step 3: Get App Credentials
1. Go to "Settings" → "Basic"
2. Copy the App ID and App Secret

### Step 4: Update Environment Variables
```env
FACEBOOK_CLIENT_ID=your-facebook-app-id-here
FACEBOOK_CLIENT_SECRET=your-facebook-app-secret-here
```

## 3. X/Twitter OAuth Setup

### Step 1: Create Twitter App
1. Go to [Twitter Developer Portal](https://developer.twitter.com/)
2. Apply for a developer account if you don't have one
3. Create a new app in your developer dashboard

### Step 2: Configure OAuth 2.0
1. In your app settings, go to "Authentication settings"
2. Enable OAuth 2.0
3. Add Callback URLs:
   - `http://localhost:8000/auth/twitter/callback` (development)
   - `https://yourdomain.com/auth/twitter/callback` (production)
4. Set App permissions to "Read" (minimum required)

### Step 3: Get API Keys
1. Go to "Keys and tokens" tab
2. Copy the API Key (Client ID) and API Secret Key (Client Secret)

### Step 4: Update Environment Variables
```env
TWITTER_CLIENT_ID=your-twitter-api-key-here
TWITTER_CLIENT_SECRET=your-twitter-api-secret-here
```

## 4. Database Migration

After updating your environment variables, you need to update your database schema:

```bash
cd backend
python init_db.py
```

This will add the new OAuth fields to your User table.

## 5. Testing the Setup

1. Start your backend server:
   ```bash
   cd backend
   uvicorn app.main:app --reload
   ```

2. Start your frontend server:
   ```bash
   cd frontend
   npm run dev
   ```

3. Navigate to `http://localhost:3000/login` or `http://localhost:3000/register`
4. Try clicking on the social login buttons

## 6. Production Deployment

### Environment Variables for Production
Make sure to update your production environment with:

```env
FRONTEND_URL=https://your-frontend-domain.com
CORS_ORIGINS=https://your-frontend-domain.com
```

### OAuth Redirect URLs
Update all OAuth provider settings to include your production callback URLs:
- Google: `https://your-backend-domain.com/auth/google/callback`
- Facebook: `https://your-backend-domain.com/auth/facebook/callback`
- Twitter: `https://your-backend-domain.com/auth/twitter/callback`

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI"**: Make sure the callback URLs in your OAuth provider settings exactly match the ones your app is using.

2. **"Invalid client"**: Double-check your client ID and secret in the environment variables.

3. **CORS errors**: Ensure your `CORS_ORIGINS` environment variable includes your frontend URL.

4. **Twitter email issue**: Twitter doesn't provide email addresses by default. The implementation uses a placeholder email format.

### Debug Mode
Set `ENV=development` in your `.env` file to see detailed error messages in the console.

## Security Notes

1. Never commit your `.env` file to version control
2. Use strong, unique secrets for production
3. Regularly rotate your OAuth client secrets
4. Monitor your OAuth provider dashboards for unusual activity
5. Consider implementing rate limiting for OAuth endpoints

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Check the backend logs for detailed error information
3. Verify all environment variables are set correctly
4. Ensure all OAuth provider settings match your configuration
